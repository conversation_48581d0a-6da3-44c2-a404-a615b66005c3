using System;
using System.Threading.Tasks;
using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// Order service interface
    /// </summary>
    public interface IOrderService
    {
        /// <summary>
        /// Get paginated list of orders
        /// </summary>
        /// <param name="query">Filter input for orders</param>
        /// <returns>Paginated list of orders</returns>
        Task<CommonListPage> GetPageAsync(OrderFilterInput query);

        /// <summary>
        /// Get order information by ID
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Order information</returns>
        Task<CommonViewOidInfo> GetInfoAsync(Guid? oid);

        /// <summary>
        /// Create or update order
        /// </summary>
        /// <param name="info">Order information</param>
        /// <returns>Validation result with order ID</returns>
        Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info);

        /// <summary>
        /// Delete order
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> DeleteAsync(Guid? oid);
    }
}
