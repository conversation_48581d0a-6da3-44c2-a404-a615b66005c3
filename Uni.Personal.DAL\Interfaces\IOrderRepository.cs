using UNI.Common.CommonBase;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Interfaces
{
    /// <summary>
    /// Order repository interface
    /// </summary>
    public interface IOrderRepository : IBaseRepository<OrderFilterInput>
    {
        // Inherits all methods from IBaseRepository:
        // - Task<CommonListPage> GetPageAsync(OrderFilterInput filter);
        // - Task<CommonViewOidInfo> GetInfoAsync(Guid? oid);
        // - Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info);
        // - Task<BaseValidate> DeleteAsync(Guid? oid);
    }
}
