using System;
using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Filter input for Order pagination
    /// </summary>
    public class OrderFilterInput : FilterInput
    {
        public string? OrderCode { get; set; }
        public string? CustomerName { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? Status { get; set; }
        public string? ProductType { get; set; }
    }

    /// <summary>
    /// Order information model for create/update operations
    /// </summary>
    public class OrderInfo : CommonViewOidInfo
    {
        public string? OrderCode { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerPhone { get; set; }
        public string? ProductType { get; set; }
        public decimal? Amount { get; set; }
        public int? Status { get; set; }
        public DateTime? OrderDate { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Order list item for display in grid
    /// </summary>
    public class OrderListItem
    {
        public Guid Oid { get; set; }
        public string? OrderCode { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerEmail { get; set; }
        public string? ProductType { get; set; }
        public decimal? Amount { get; set; }
        public int? Status { get; set; }
        public string? StatusText { get; set; }
        public DateTime? OrderDate { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
    }
}
