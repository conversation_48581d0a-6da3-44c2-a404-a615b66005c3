using Dapper;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using UNI.Model.Commons;
using UNI.Model;
using Microsoft.Extensions.Configuration;
using DapperParameters;
using System.Reflection.Metadata;
using System.Threading;

namespace UNI.Common.CommonBase
{
    public class UniBaseRepository : IUniBaseRepository
    {
        //store filter dùng chung
        //Hàm delegate để xử lý tham số truyền vào cho stored procedure
        public delegate DynamicParameters ParametersHandler(DynamicParameters param);
        //Hàm delegate để xử lý đọc thêm thông tin khác từ DB nếu cần
        public delegate void UniReaderHandler(SqlMapper.GridReader result);
        public delegate T UniReaderHandler<T>(T data, SqlMapper.GridReader result);
        public delegate List<T> UniReaderHandlerList<T>(List<T> data, SqlMapper.GridReader result);
        public delegate T ReaderDataHandler<T>(T data, SqlMapper.GridReader result) where T : CommonDataPage;
        //Hàm delegate truyền thêm các tham số để lấy dữ liệu dạng DataSet
        public delegate void UniSqlParameterCollection(SqlParameterCollection param);

        protected readonly IUniCommonBaseRepository CommonBase;

        public CommonInfo CommonInfo => CommonBase.CommonInfo;
        public CommonInfo CtrlClient => CommonBase.CommonInfo;
        public IConfiguration Configuration => CommonBase.Configuration;

        public UniBaseRepository(IUniCommonBaseRepository common)
        {
            CommonBase = common;
        }

        #region GetTableFilter
        /// <summary>
        /// Thông tin các control được cấu hình cho phần tìm kiếm nâng cao
        /// </summary>
        /// <param name="tableKey"></param>
        /// <returns></returns>
        public Task<CommonViewInfo> GetTableFilter(string tableKey)
            => GetTableFilterCommon(tableKey);

        /// <summary>
        /// Thông tin các control được cấu hình cho phần tìm kiếm nâng cao
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public Task<CommonViewInfo> GetTableFilter(string storedProcedure, object parameters)//object parameters = null)
            => GetFilterAsync(storedProcedure, parameters);

        public virtual Task<CommonViewInfo> GetTableFilterAsync(string tableKey, object parameters = null)
        {
            var param = GetParameters(null, parameters, null);
            param.Add("@tableKey", tableKey);

            return GetFieldsAsync<CommonViewInfo>(CommonInfo.CommonFilterStored, param);
        }
        #endregion

        #region GetFilter
        public Task<CommonViewInfo> GetTableFilter(string storedProcedure, ParametersHandler parametersHandler)
        {
            var param = GetParameters(null, null, parametersHandler);
            return GetFilterAsync<CommonViewInfo>(storedProcedure, param);
        }

        /// <summary>
        /// Thông tin các control được cấu hình cho phần tìm kiếm nâng cao
        /// </summary>
        /// <param name="tableKey"></param>
        /// <returns></returns>
        public Task<CommonViewInfo> GetTableFilterCommon(string tableKey)
            => GetFilterAsync<CommonViewInfo>(CommonInfo.CommonFilterStored, null, new { tableKey });

        /// <summary>
        /// Thông tin các control được cấu hình cho phần tìm kiếm nâng cao
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="parameters"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<CommonViewInfo> GetFilterAsync(string storedProcedure, object parameters = null,
            ParametersHandler parametersHandler = null, UniReaderHandler<CommonViewInfo> readerHandler = null)
            => GetFilterAsync(storedProcedure, null, parameters, parametersHandler, readerHandler);

        /// <summary>
        /// Thông tin các control được cấu hình cho phần tìm kiếm nâng cao
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParam"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<T> GetFilterAsync<T>(string storedProcedure, DynamicParameters dynamicParam, object objParam = null,
            ParametersHandler parametersHandler = null, UniReaderHandler<T> readerHandler = null) where T : CommonViewInfo
            => GetFirstOrDefaultAsync<T>(storedProcedure, dynamicParam, objParam, parametersHandler,
                (data, reader) =>
                {
                    if (data != null)
                    {
                        data.group_fields = reader.Read<viewGroup>().ToList();
                        if (data.group_fields != null && data.group_fields.Count > 0)
                        {
                            var fields = reader.Read<viewField>().ToList();
                            foreach (var gr in data.group_fields)
                                gr.fields = fields.Where(f => f.group_cd == gr.group_cd).ToList();
                        }
                    }
                    data = readerHandler != null ? readerHandler(data, reader) : data;
                    return data;
                });
        #endregion

        #region GetPage
        /// <summary>
        /// 
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="filter"></param>
        /// <param name="objParams"></param>
        /// <returns></returns>
        [Obsolete("Nên dùng GetPageAsync thay thế để tránh blocking")]
        public virtual CommonListPage GetPage(string storedProcedure, FilterInput filter, object objParams = null)
        {
            return this.GetPageAsync(storedProcedure, filter, objParams).GetAwaiter().GetResult();
        }
        //public static T ReadCommonListPage<T>(FilterInput filter, DynamicParameters param,
        //    SqlMapper.GridReader result) where T : CommonListPage, new()
        //{
        //    var data = new T();

        //    if (filter?.offSet is null or 0)
        //    {
        //        data.gridflexs = result.Read<viewGridFlex>().ToList();
        //    }

        //    var listData = result.Read<object>().ToList();
        //    data.dataList = new ResponseList<List<object>>(listData, param.Get<long>("@total"),
        //        param.Get<long>("@totalFiltered"), param.Get<string>("@gridKey"));
        //    return data;
        //}
        
        /// <summary>
        /// Danh sách dữ liệu phân trang
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="filter"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<CommonListPage> GetPageAsync(string storedProcedure, FilterInput filter = null,
           ParametersHandler parametersHandler = null, UniReaderHandler readerHandler = null)
            => GetPageAsync<CommonListPage, object>(storedProcedure, filter, null, parametersHandler,
                (data, reader) =>
                {
                    readerHandler?.Invoke(reader);
                    return data;
                });

        public Task<CommonListPage> GetPageAsync(string storedProcedure, FilterBase filter = null,
           ParametersHandler parametersHandler = null, UniReaderHandler readerHandler = null)
            => GetPageAsync<CommonListPage, object>(storedProcedure, filter, null, parametersHandler,
                (data, reader) =>
                {
                    readerHandler?.Invoke(reader);
                    return data;
                });

        public Task<CommonListPage> GetPageAsync(string storedProcedure, FilterInput filter = null, object objParams = null,
           ParametersHandler parametersHandler = null, UniReaderHandler readerHandler = null)
            => GetPageAsync<CommonListPage, object>(storedProcedure, filter, objParams, parametersHandler,
                (data, reader) =>
                {
                    readerHandler?.Invoke(reader);
                    return data;
                });

        /// <summary>
        /// Danh sách dữ liệu phân trang
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="filter"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<viewBasePage<T>> GetPageAsync<T>(string storedProcedure, FilterInput filter = null,
            ParametersHandler parametersHandler = null, UniReaderHandler readerHandler = null)
            => GetPageAsync<viewBasePage<T>, T>(storedProcedure, filter, null, parametersHandler,
                (data, reader) =>
                {
                    readerHandler?.Invoke(reader);
                    return data;
                });

        /// <summary>
        /// Danh sách dữ liệu phân trang
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<viewBasePage<T>> GetPageAsync<T>(string storedProcedure, object objParams,
            ParametersHandler parametersHandler = null, UniReaderHandler<viewBasePage<T>> readerHandler = null)
            => GetPageAsync<T>(storedProcedure, null, objParams, parametersHandler, readerHandler);

        /// <summary>
        /// Danh sách dữ liệu phân trang
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="filter"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<viewBasePage<T>> GetPageAsync<T>(string storedProcedure, FilterBase filter,
            object objParams = null, ParametersHandler parametersHandler = null, UniReaderHandler<viewBasePage<T>> readerHandler = null)
            => GetPageAsync<viewBasePage<T>, T>(storedProcedure, filter, objParams, parametersHandler, readerHandler);

        /// <summary>
        /// Danh sách dữ liệu phân trang
        /// </summary>
        /// <typeparam name="V"></typeparam>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="filter"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        private async Task<TV> GetPageAsync<TV, T>(string storedProcedure, FilterInput filter = null,
            object objParams = null, ParametersHandler parametersHandler = null, UniReaderHandler<TV> readerHandler = null)
            where TV : viewBasePage<T>, new()
        {
            var dynamicParam = new DynamicParameters();
            GetCommonPageParameters(ref dynamicParam, filter);

            var data = new TV();
            await GetMultipleAsync(storedProcedure, dynamicParam, objParams, parametersHandler,
                reader =>
                {
                    if (filter?.offSet is null or 0)
                        data.gridflexs = reader.Read<viewGridFlex>().ToList();

                    var listData = reader.Read<T>().ToList();
                    data = readerHandler != null ? readerHandler(data, reader) : data;
                    data.dataList = new ResponseList<List<T>>(listData, dynamicParam.Get<long>("@total"),
                        dynamicParam.Get<long>("@totalFiltered"), dynamicParam.Get<string>("@gridKey"));
                });
            return data;
        }

        private void GetCommonPageParameters(ref DynamicParameters dynamicParam, FilterInput filter = null)
        {
            filter ??= new FilterInput();

            dynamicParam.Add("@filter", filter.filter);
            dynamicParam.Add("@offset", filter.offSet);
            dynamicParam.Add("@pageSize", filter.pageSize);
            dynamicParam.Add("@gridWidth", filter.gridWidth);
            //Comment để nhứng page k có chưa lỗi
            if (filter.customOid != null)
            {
                dynamicParam.Add("@customOid", filter.customOid);
            }
            dynamicParam.Add("@total", 0, DbType.Int64, ParameterDirection.InputOutput);
            dynamicParam.Add("@totalFiltered", 0, DbType.Int64, ParameterDirection.InputOutput);
            dynamicParam.Add("@gridKey", "", DbType.String, ParameterDirection.InputOutput);
        }
        #endregion

        #region GetDataPage
        public async Task<T> GetDataListPage<T>(string storedProcedure,
            FilterInput filter, ParametersHandler parametersHandler = null, ReaderDataHandler<T> readerHandler = null) where T : CommonDataPage, new()
        {
            try
            {
                using var connection = new SqlConnection(CommonInfo.ConnectionString);
                await connection.OpenAsync();

                var param = this.ToDynamicParamDatas(filter);
                param = parametersHandler != null ? parametersHandler(param) : param;

                var result = await connection.QueryMultipleAsync(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                var data = ReadDataListPage<T>(filter, param, result, readerHandler);
                return data;
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message, ex);
            }
        }

        public T ReadDataListPage<T>(FilterInput filter, DynamicParameters param,
            SqlMapper.GridReader result, ReaderDataHandler<T> readerHandler = null) where T : CommonDataPage, new()
        {
            var data = result.ReadFirstOrDefault<T>();
            if (filter?.offSet is null or 0)
            {
                data.gridflexs = result.Read<viewGridFlex>().ToList();
            }
            data.dataList = result.Read<object>().ToList();
            data = readerHandler != null ? readerHandler(data, result) : data;
            return data;
        }
        #endregion
        #region GetFields
        public virtual async Task<T> GetFieldsAsync<T>(string storedProcedure, object objParams = null, Func<T, SqlMapper.GridReader, Task<T>> readerHandler = null) where T : viewBaseInfo
        {
            await using var connection = new SqlConnection(CommonInfo.ConnectionString);
            await connection.OpenAsync();
            var param = GetParameters(null, objParams);

            using var reader = await connection.QueryMultipleAsync(storedProcedure, param,
                commandType: CommandType.StoredProcedure);
            var data = await ReadCommonFieldsAsync(reader, readerHandler);
            return data;
        }
        private static async Task<T> ReadCommonFieldsAsync<T>(SqlMapper.GridReader reader, Func<T, SqlMapper.GridReader, Task<T>> readerHandler = null) where T : viewBaseInfo
        {
            var data = await reader.ReadFirstOrDefaultAsync<T>();
            if (data == null) return null;

            data.group_fields = (await reader.ReadAsync<viewGroup>()).ToList();
            if (data.group_fields == null || data.group_fields.Count <= 0) return data;
            var fields = (await reader.ReadAsync<viewField>()).ToList();
            foreach (var gr in data.group_fields)
            {
                gr.fields = fields.Where(f => f.group_cd == gr.group_cd).ToList();
            }
            data = readerHandler != null ? await readerHandler(data, reader) : data;
            return data;
        }

        public virtual Task<viewBaseInfo> GetFieldsAsync(string storedProcedure, object objParams = null,
            ParametersHandler parametersHandler = null, UniReaderHandler<viewBaseInfo> readerHandler = null)
            => GetFirstOrDefaultAsync<viewBaseInfo>(storedProcedure, null, objParams, parametersHandler,
                (data, reader) =>
                {
                    data.group_fields = reader.Read<viewGroup>().ToList();
                    if (data.group_fields == null || data.group_fields.Count <= 0)
                        return data;

                    var fields = reader.Read<viewField>().ToList();
                    foreach (var gr in data.group_fields)
                        gr.fields = fields.Where(f => f.group_cd == gr.group_cd).ToList();

                    return readerHandler != null ? readerHandler(data, reader) : data;
                });

        /// <summary>
        /// Lấy thông tin cấu hình Fields để thêm/Sửa dữ liệu
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<T> GetFieldsAsync<T>(string storedProcedure, ParametersHandler parametersHandler = null,
            UniReaderHandler<T> readerHandler = null) where T : viewBaseInfo
            => GetFieldsAsync<T>(storedProcedure, null, null, parametersHandler, readerHandler);

        /// <summary>
        /// Lấy thông tin cấu hình Fields để thêm/Sửa dữ liệu
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<T> GetFieldsAsync<T>(string storedProcedure, DynamicParameters dynamicParam = null,
            object objParams = null, ParametersHandler parametersHandler = null, UniReaderHandler<T> readerHandler = null)
            where T : viewBaseInfo
            => GetFirstOrDefaultAsync<T>(storedProcedure, dynamicParam, objParams, parametersHandler,
                (data, reader) =>
                {
                    data.group_fields = reader.Read<viewGroup>().ToList();
                    if (data.group_fields == null || data.group_fields.Count <= 0)
                        return data;

                    var fields = reader.Read<viewField>().ToList();
                    foreach (var gr in data.group_fields)
                    {
                        gr.fields = fields.Where(f => f.group_cd == gr.group_cd).ToList();
                    }

                    return readerHandler != null ? readerHandler(data, reader) : data;
                });

        private static T ReadCommonFields<T>(SqlMapper.GridReader reader, UniReaderHandler<T> readerHandler = null)
            where T : viewBaseInfo
        {
            var data = reader.ReadFirstOrDefault<T>();
            if (data == null) return null;

            data.group_fields = reader.Read<viewGroup>().ToList();
            if (data.group_fields == null || data.group_fields.Count <= 0) return data;
            var fields = reader.Read<viewField>().ToList();
            foreach (var gr in data.group_fields)
            {
                gr.fields = fields.Where(f => f.group_cd == gr.group_cd).ToList();
            }

            data = readerHandler != null ? readerHandler(data, reader) : data;
            return data;
        }
        #endregion

        #region SetInfoAsync
        public virtual async Task<T> SetInfoAsync<T>(string storedProcedure, viewBaseInfo viewBaseInfo)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(null, null);
                if (viewBaseInfo != null)
                {
                    var obj = viewBaseInfo.ToObject();
                    param.AddDynamicParams(obj);
                }
                var result = await connection.QueryFirstAsync<T>(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                return result;
            }
        }
        public virtual async Task<T> SetInfoAsync<T>(string storedProcedure, viewBaseInfo viewBaseInfo, object objParams = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(null, objParams);
                if (viewBaseInfo != null)
                {
                    var obj = viewBaseInfo.ToObject();
                    param.AddDynamicParams(obj);
                }

                var result = await connection.QueryFirstAsync<T>(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                return result;
            }
        }
        public virtual async Task<T> SetInfoAsync<T>(string storedProcedure, viewBaseInfo viewBaseInfo, object objParams = null, ParametersHandler parametersHandler = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var obj = viewBaseInfo.ToObject();
                var param = GetParameters(null, objParams, parametersHandler);
                param.AddDynamicParams(obj);
                var result = await connection.QueryFirstAsync<T>(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                return result;
            }
        }
        public virtual async Task<T> SetInfoAsync<T>(string storedProcedure, viewBaseInfo viewBaseInfo, ParametersHandler parametersHandler = null,
            Func<T, SqlMapper.GridReader, Task<T>> readerHandler = null) where T : viewBaseInfo
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var obj = viewBaseInfo.ToObject();
                var param = GetParameters(null, null, parametersHandler);
                param.AddDynamicParams(obj);
                using var reader = await connection.QueryMultipleAsync(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                var data = await ReadCommonFieldsAsync(reader, readerHandler);
                return data;
            }
        }
        /// <summary>
        /// Create/update từ form fields
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="info"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public virtual async Task<BaseValidate> SetInfoAsync(string storedProcedure, viewBaseInfo viewBaseInfo, object parameters = null)
        {
            var rs = await SetInfoAsync<BaseValidate<string>>(storedProcedure, viewBaseInfo, parameters);
            return new BaseValidate()
            {
                valid = rs.valid,
                messages = rs.messages,
                code = rs.code,
                id = rs.id
            };
        }
        #endregion

        #region SetAsync
        /// <summary>
        /// Create/update
        /// </summary>
        /// <typeparam name="T">Giá trị trả về(vd: Id của bản ghi)</typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <returns></returns>
        public virtual async Task<BaseValidate<T>> SetAsync<T>(string storedProcedure, object objParams,
            ParametersHandler parametersHandler = null, UniReaderHandler<BaseValidate<T>> readerHandler = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(null, objParams, parametersHandler);
                using var reader = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure);
                var result = reader.ReadFirstOrDefault<BaseValidate<T>>();
                result = readerHandler != null ? readerHandler(result, reader) : result;
                return result;
            }
        }

        /// <summary>
        /// Create/update
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public virtual async Task<BaseValidate> SetAsync(string storedProcedure, object objParams,
            ParametersHandler parametersHandler = null, UniReaderHandler<BaseValidate> readerHandler = null, int? commandTimeout = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(null, objParams, parametersHandler);
                using var reader = await connection.QueryMultipleAsync(storedProcedure, param, commandTimeout: commandTimeout, commandType: CommandType.StoredProcedure);
                var result = reader.ReadFirstOrDefault<BaseValidate>();
                result = readerHandler != null ? readerHandler(result, reader) : result;
                return result;
            }
        }

        public virtual async Task<T> SetAsync<T>(string storedProcedure, ParametersHandler parametersHandler = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(null, null, parametersHandler);
                var result = await connection.QueryFirstAsync<T>(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                return result;
            }
        }
        protected virtual async Task<int> ExecuteAsync(string storedProcedure, object parameters = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(null, parameters, null);
                var result = await connection.ExecuteAsync(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                return result;
            }
        }
        #endregion

        #region GetAsync
        /// <summary>
        /// Get object list
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <returns></returns>
        public virtual async Task<IEnumerable<T>> GetAsync<T>(string storedProcedure, object objParams = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(null, objParams, null);
                var result = await connection.QueryAsync<T>(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                return result;
            }
        }
        #endregion

        #region DeleteAsync
        public virtual async Task<BaseValidate> DeleteAsync(string storedProcedure, object objParams)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(null, objParams);

                var result = await connection.QueryFirstAsync<BaseValidate>(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                return result;
            }
        }
        #endregion

        #region SetImportData
        /// <summary>
        /// Xử lý Import dữ liệu Bao gồm thông tin của file Import
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TX"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dataTableType"></param>
        /// <param name="importInfo"></param>
        /// <param name="paramName"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<ImportListPage> SetImportDataWithImport<T, TX>(string storedProcedure, string dataTableType,
            TX importInfo, string paramName = "ArrDataImport",
            ParametersHandler parametersHandler = null, UniReaderHandler readerHandler = null)
            where T : new() where TX : BaseImportSet<T>, new()
            => SetImportData<T, TX>(storedProcedure, dataTableType, importInfo, paramName, importInfo.importFile, parametersHandler, readerHandler);

        /// <summary>
        /// Xử lý Import dữ liệu
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="TX"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dataTableType"></param>
        /// <param name="importInfo"></param>
        /// <param name="paramName"></param>
        /// <param name="importFile"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public async Task<ImportListPage> SetImportData<T, TX>(string storedProcedure, string dataTableType,
            TX importInfo, string paramName = "ArrDataImport", object importFile = null,
            ParametersHandler parametersHandler = null, UniReaderHandler readerHandler = null)
            where T : new() where TX : BaseImportSet<T>, new()
        {
            try
            {
                var param = new DynamicParameters();
                param = parametersHandler != null ? parametersHandler(param) : param;
                param.AddTable(paramName, dataTableType, importInfo.imports);
                param.Add("Accept", importInfo.accept);

                if (importFile != null)
                    param.AddDynamicParams(importFile);

                var importPage = new ImportListPage();
                await this.GetMultipleAsync(storedProcedure, param, null,
                    reader =>
                    {
                        importPage = reader.ReadFirstOrDefault<ImportListPage>();
                        if (importPage != null)
                        {
                            importPage.gridflexs = reader.Read<viewGridFlex>().ToList();
                            importPage.dataList = reader.Read<object>().ToList();
                            importPage.importFile = importInfo.importFile;
                        }
                        readerHandler?.Invoke(reader);
                    });
                return importPage;
            }
            catch (Exception ex)
            {
                return new ImportListPage { messages = ex.Message };
            }
        }
        /// <summary>
        /// SetImport - xử lý import
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <typeparam name="K"></typeparam>
        /// <param name="storedProcedure"> Tên stored </param>
        /// <param name="importInfo">Class import (K)</param>
        /// <param name="dataTableName"> Tên bảng lưu (mặc định "ArrDataImport") </param>
        /// <param name="dataTableType"> Type bảng lưu </param>
        /// <param name="parameters"> object param cần truyền</param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public async Task<ImportListPage> SetImport<T, K>(
            string storedProcedure, K importInfo,
            string dataTableName = "ArrDataImport",
            string dataTableType = null,
            object parameters = null,
            ParametersHandler parametersHandler = null,
            UniReaderHandler readerHandler = null)
            where T : new()
            where K : BaseImportSet<T>, new()
        {
            try
            {
                var param = new DynamicParameters();
                param = parametersHandler != null ? parametersHandler(param) : param;

                param.Add("Accept", importInfo.accept);
                if (parameters != null)
                    param.AddDynamicParams(parameters);
                if (importInfo.importFile != null)
                    param.AddDynamicParams(importInfo.importFile);
                param.AddTable(dataTableName, dataTableType, importInfo.imports);

                var page = new ImportListPage();
                await this.GetMultipleAsync(storedProcedure, param, null,
                    reader =>
                    {
                        page = reader.ReadFirstOrDefault<ImportListPage>();
                        if (page.valid == true)
                        {
                            page.gridflexs = reader.Read<viewGridFlex>().ToList();
                            page.dataList = reader.Read<object>().ToList();
                            page.importFile = reader.ReadFirst<uImportFile>();
                        }
                        readerHandler?.Invoke(reader);
                    });
                return page;
            }
            catch (Exception ex)
            {
                return new ImportListPage { messages = ex.Message };
            }
        }
        #endregion

        #region GetDataSet
        /// <summary>
        /// Trả về các kết quả thông qua DataSet
        /// </summary>
        /// <param name="storedName"></param>
        /// <param name="paramCollection"></param>
        /// <returns></returns>
        public Task<DataSet> GetDataSetAsync(string storedName, UniSqlParameterCollection paramCollection = null, int? commandTimeout = null)
            => GetDataSetAsync(storedName, null, paramCollection,commandTimeout);

        /// <summary>
        /// Trả về các kết quả thông qua DataSet
        /// </summary>
        /// <param name="storedName"></param>
        /// <param name="lsParameter">dạng Dictionary với Key là parameterName, Value là Dictionary gồm kiểu dữ liệu và giá trị tham số</param>
        /// <returns></returns>
        public Task<DataSet> GetDataSetAsync(string storedName, Dictionary<string, Dictionary<SqlDbType, object>> lsParameter)
            => GetDataSetAsync(storedName, null,
                param =>
                {
                    foreach (var parameter in lsParameter)
                        param.Add(parameter.Key, parameter.Value.FirstOrDefault().Key).Value = parameter.Value.FirstOrDefault().Value;
                });

        /// <summary>
        /// Hàm xử lý chung để lấy kết quả dạng DataSet
        /// </summary>
        /// <param name="storedName"></param>
        /// <param name="sqlCommand"></param>
        /// <returns></returns>
        private async Task<DataSet> GetDataSetAsync(string storedName, SqlCommand sqlCommand = null, UniSqlParameterCollection paramCollection = null, int? commandTimeout = null)
        {
            await using var connection = new SqlConnection(CommonInfo.ConnectionString);
            var ds = new DataSet();    
            await connection.OpenAsync();
            var cmd = new SqlCommand(storedName, connection) { CommandType = CommandType.StoredProcedure };
            cmd.CommandTimeout = commandTimeout ?? 200;
            sqlCommand?.Parameters.Cast<SqlParameter>().ToList().ForEach(p =>
            {
                cmd.Parameters.Add((SqlParameter)((ICloneable)p).Clone());
               
            });
            paramCollection?.Invoke(cmd.Parameters);
            
           
            //check key exist
            var userIdKeys = new []{ "@userId", "userId", "UserId", "@UserId" };
            var userIdKeyParameter = cmd.Parameters.Cast<SqlParameter>().FirstOrDefault(p => userIdKeys.Contains(p.ParameterName));
            if (userIdKeyParameter == null)
            {
                cmd.Parameters.Add("@userId", SqlDbType.NVarChar);
                cmd.Parameters["@userId"].Value = CommonInfo.UserId;
            }
            var da = new SqlDataAdapter(cmd);
            da.Fill(ds);
            return ds;
        }

        void CopySqlParameters(SqlParameterCollection source, SqlParameterCollection destination)
            => source.Cast<SqlParameter>().ToList().ForEach(p => destination.Add((SqlParameter)((ICloneable)p).Clone()));
        #endregion

        #region GetFirstOrDefault
        /// <summary>
        /// Get object list
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public virtual async Task<T> GetFirstOrDefaultAsync<T>(string storedProcedure, object parameters = null)
        {
            await using var connection = new SqlConnection(CommonInfo.ConnectionString);
            await connection.OpenAsync();
            var param = GetParameters(null, parameters, null);
            //param.AddDynamicParams(parameters);
            var result = await connection.QueryFirstOrDefaultAsync<T>(storedProcedure, param,
                commandType: CommandType.StoredProcedure);
            return result;
        }
        /// <summary>
        /// Trả về mặc định 1 bản ghi đầu tiên và đọc thêm các kết quả khác nếu có thông qua readerHandler
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="parametersHandler"></param>
        /// <returns></returns>
        public Task<T> GetFirstOrDefaultAsync<T>(string storedProcedure, ParametersHandler parametersHandler, int? commandTimeout)
            => GetFirstOrDefaultAsync<T>(storedProcedure, null, null, parametersHandler, null, commandTimeout);

        /// <summary>
        /// Trả về mặc định 1 bản ghi đầu tiên và đọc thêm các kết quả khác nếu có thông qua readerHandler
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public async Task<T> GetFirstOrDefaultAsync<T>(string storedProcedure, ParametersHandler parametersHandler,
            UniReaderHandler<T> readerHandler = null, int? commandTimeout = null)
        {
            await using var connection = new SqlConnection(CommonInfo.ConnectionString);
            await connection.OpenAsync();
            var param = GetParameters(null, null, parametersHandler);

            using var reader = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure, commandTimeout: commandTimeout);
            var data = await reader.ReadFirstOrDefaultAsync<T>();
            data = readerHandler != null ? readerHandler(data, reader) : data;
            return data;
        }

        /// <summary>
        /// Trả về mặc định 1 bản ghi đầu tiên và đọc thêm các kết quả khác nếu có thông qua readerHandler
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<T> GetFirstOrDefaultAsync<T>(string storedProcedure, object objParams = null,
            ParametersHandler parametersHandler = null, UniReaderHandler<T> readerHandler = null, int? commandTimeout = null)
            => GetFirstOrDefaultAsync(storedProcedure, null, objParams, parametersHandler, readerHandler, commandTimeout);

        /// <summary>
        /// Trả về mặc định 1 bản ghi đầu tiên và đọc thêm các kết quả khác nếu có thông qua readerHandler
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<T> GetFirstOrDefaultAsync<T>(string storedProcedure, DynamicParameters dynamicParam,
            object objParams = null, ParametersHandler parametersHandler = null, UniReaderHandler<T> readerHandler = null, int? commandTimeout = null)
            => GetMultipletObjectAsync(storedProcedure, dynamicParam, objParams, parametersHandler, readerHandler, commandTimeout);

        [Obsolete("Nên dùng GetFirstOrDefaultAsync thay thế để tránh blocking")]
        public virtual T GetFirstOrDefault<T>(string storedProcedure, object parameters = null)
        {
            return this.GetFirstOrDefaultAsync<T>(storedProcedure, parameters).GetAwaiter().GetResult();
        }
        #endregion

        #region GetList
        public virtual async Task<List<T>> GetListAsync<T>(string storedProcedure, object parameters = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(null, parameters, null);
                var result = await connection.QueryAsync<T>(storedProcedure, param,
                    commandType: CommandType.StoredProcedure);
                return result.ToList();
            }
        }
        /// <summary>
        /// Kết quả trả về dạng List và lấy các kết quả khác nếu có
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        [Obsolete("Nên dùng GetIEnumerableAsync, GetListAsync thay thế để tránh blocking")]
        public List<T> GetList<T>(string storedProcedure, DynamicParameters dynamicParam = null, object objParams = null,
            ParametersHandler parametersHandler = null, UniReaderHandlerList<T> readerHandler = null)
            => GetIEnumerableAsync<T>(storedProcedure, dynamicParam, objParams, parametersHandler, readerHandler).GetAwaiter().GetResult().ToList();

        /// <summary>
        /// Kết quả trả về dạng List và lấy các kết quả khác nếu có
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        [Obsolete("Nên dùng GetIEnumerableAsync, GetListAsync thay thế để tránh blocking")]
        public List<T> GetList<T>(string storedProcedure, object objParams,
            ParametersHandler parametersHandler = null, UniReaderHandlerList<T> readerHandler = null)
            => GetIEnumerableAsync<T>(storedProcedure, null, objParams, parametersHandler, readerHandler).GetAwaiter().GetResult().ToList();
        
        /// <summary>
        /// Kết quả trả về dạng List và lấy các kết quả khác nếu có
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="parametersHandler"></param>
        /// <returns></returns>
        public async Task<List<T>> GetListAsync<T>(string storedProcedure, ParametersHandler parametersHandler)
            => (await GetIEnumerableAsync<T>(storedProcedure, null, null, parametersHandler, null)).ToList();

        /// <summary>
        /// Kết quả trả về dạng List và lấy các kết quả khác nếu có
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public async Task<List<T>> GetListAsync<T>(string storedProcedure, DynamicParameters dynamicParam = null, object objParams = null,
            ParametersHandler parametersHandler = null, UniReaderHandlerList<T> readerHandler = null)
            => (await GetIEnumerableAsync<T>(storedProcedure, dynamicParam, objParams, parametersHandler, readerHandler)).ToList();

        /// <summary>
        /// Kết quả trả về dạng List và lấy các kết quả khác nếu có
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public async Task<List<T>> GetListAsync<T>(string storedProcedure, object objParams,
            ParametersHandler parametersHandler = null, UniReaderHandlerList<T> readerHandler = null)
            => (await GetIEnumerableAsync<T>(storedProcedure, null, objParams, parametersHandler, readerHandler)).ToList();
        #endregion

        #region GetIEnumerable
        /// <summary>
        /// Kết quả trả về dạng IEnumerable và lấy các kết quả khác nếu có
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public IEnumerable<T> GetIEnumerable<T>(string storedProcedure, DynamicParameters dynamicParam = null, object objParams = null,
            ParametersHandler parametersHandler = null, UniReaderHandlerList<T> readerHandler = null, int? commandTimeout = null)
            => GetIEnumerableAsync<T>(storedProcedure, dynamicParam, objParams, parametersHandler, readerHandler, commandTimeout).GetAwaiter().GetResult();

        /// <summary>
        /// Kết quả trả về dạng IEnumerable và lấy các kết quả khác nếu có
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        [Obsolete("Nên dùng GetIEnumerableAsync, GetListAsync thay thế để tránh blocking")]
        public IEnumerable<T> GetIEnumerable<T>(string storedProcedure, object objParams,
            ParametersHandler parametersHandler = null, UniReaderHandlerList<T> readerHandler = null, int? commandTimeout = null)
            => GetIEnumerableAsync<T>(storedProcedure, null, objParams, parametersHandler, readerHandler, commandTimeout).GetAwaiter().GetResult();

        /// <summary>
        /// Kết quả trả về dạng IEnumerable và lấy các kết quả khác nếu có
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<IEnumerable<T>> GetIEnumerableAsync<T>(string storedProcedure, object objParams,
            ParametersHandler parametersHandler = null, UniReaderHandlerList<T> readerHandler = null, int? commandTimeout = null)
            => GetIEnumerableAsync<T>(storedProcedure, null, objParams, parametersHandler, readerHandler, commandTimeout);

        /// <summary>
        /// Kết quả trả về dạng IEnumerable và lấy các kết quả khác nếu có
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task<IEnumerable<T>> GetIEnumerableAsync<T>(string storedProcedure, DynamicParameters dynamicParam = null, object objParams = null,
            ParametersHandler parametersHandler = null, UniReaderHandlerList<T> readerHandler = null, int? commandTimeout = null)
            => GetMultipletListObjectAsync<T>(storedProcedure, dynamicParam, objParams, parametersHandler, readerHandler, commandTimeout);
        #endregion

        #region GetMultiple
        /// <summary>
        /// Trả về bản ghi đầu tiên và các kết quả khác nếu có thông qua readerHandler
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public async Task<T> GetMultipletObjectAsync<T>(string storedProcedure, DynamicParameters dynamicParam = null,
            object objParams = null, ParametersHandler parametersHandler = null, UniReaderHandler<T> readerHandler = null, int? commandTimeout = null)
        {
            var objData = new object();
            await GetMultipleAsync(storedProcedure, dynamicParam, objParams, parametersHandler,
                reader =>
                {
                    var objDataT = reader.ReadFirstOrDefault<T>();
                    objData = readerHandler != null ? readerHandler(objDataT, reader) : objDataT;
                }, commandTimeout);
            return (T)objData;
        }

        /// <summary>
        /// Trả lại dữ liệu dạng danh sách và lấy thêm các kết quả khác nếu có thông qua readerHandler
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public async Task<IEnumerable<T>> GetMultipletListObjectAsync<T>(string storedProcedure, DynamicParameters dynamicParam = null,
            object objParams = null, ParametersHandler parametersHandler = null, UniReaderHandlerList<T> readerHandler = null, int? commandTimeout = null)
        {
            var lsObjData = new List<T>();
            await GetMultipleAsync(storedProcedure, dynamicParam, objParams, parametersHandler,
                reader =>
                {
                    var lsData = reader.Read<T>();
                    lsData = readerHandler != null ? readerHandler(lsData.ToList(), reader) : lsData;
                    lsObjData.AddRange(lsData);
                }, commandTimeout);
            return lsObjData;
        }

        public virtual async Task<T> GetMultipleAsync<T>(string storedProcedure, object parameters = null,
            Func<SqlMapper.GridReader, Task<T>> readerHandler = null, int? commandTimeout = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters();
                if (parameters != null)
                    param.AddDynamicParams(parameters);

                using var reader = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure, commandTimeout: commandTimeout);
                if (reader.IsConsumed || readerHandler == null)
                    return default;

                var result = await readerHandler(reader);
                return result;
            }
        }
        public virtual async Task<T> GetMultipleAsync<T>(string storedProcedure, ParametersHandler parametersHandler = null,
            Func<SqlMapper.GridReader, Task<T>> readerHandler = null, int? commandTimeout = null)
        {
            await using (var connection = new SqlConnection(CommonInfo.ConnectionString))
            {
                await connection.OpenAsync();
                var param = GetParameters(parametersHandler: parametersHandler);
                using var reader = await connection.QueryMultipleAsync(storedProcedure, param, commandTimeout: commandTimeout,
                    commandType: CommandType.StoredProcedure);
                if (reader.IsConsumed || readerHandler == null) return default;
                var result = await readerHandler(reader);
                return result;
            }
 		}
        public Task GetMultipleAsync(string storedProcedure, ParametersHandler parametersHandler, UniReaderHandler readerHandler = null, int? commandTimeout = null)
            => GetMultipleAsync(storedProcedure, null, null, parametersHandler, readerHandler, commandTimeout);
       

        /// <summary>
        /// Lấy nhiều kết quả trong 1 Stored Procedure thông qua readerHandler (Không trả lại kết quả mặc định)
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public Task GetMultipleAsync(string storedProcedure, object objParams,
            ParametersHandler parametersHandler = null, UniReaderHandler readerHandler = null, int? commandTimeout = null)
            => GetMultipleAsync(storedProcedure, null, objParams, parametersHandler, readerHandler, commandTimeout);

        /// <summary>
        /// Lấy nhiều kết quả trong 1 Stored Procedure thông qua readerHandler (Không trả lại kết quả mặc định)
        /// </summary>
        /// <param name="storedProcedure"></param>
        /// <param name="dynamicParam"></param>
        /// <param name="objParams"></param>
        /// <param name="parametersHandler"></param>
        /// <param name="readerHandler"></param>
        /// <returns></returns>
        public async Task GetMultipleAsync(string storedProcedure, DynamicParameters dynamicParam = null,
            object objParams = null, ParametersHandler parametersHandler = null, UniReaderHandler readerHandler = null, int? commandTimeout = null)
        {
            await using var connection = new SqlConnection(CommonInfo.ConnectionString);
            await connection.OpenAsync();
            var param = GetParameters(dynamicParam, objParams, parametersHandler);
            using var reader = await connection.QueryMultipleAsync(storedProcedure, param, commandType: CommandType.StoredProcedure, commandTimeout: commandTimeout);
            readerHandler?.Invoke(reader);
        }
        #endregion

        private DynamicParameters ToDynamicParamDatas(FilterInput flt)
        {
            if (flt == null) return new DynamicParameters();
            var param = new DynamicParameters();
            param.Add("@userId", flt.userId);
            //param.Add("@acceptLanguage", flt.acceptLanguage);
            if (CommonInfo.IsAcceptLanguage)
                param.Add("AcceptLanguage", CommonInfo.AcceptLanguage);
            param.Add("@filter", flt.filter);
            param.Add("@offset", flt.offSet);
            param.Add("@pageSize", flt.pageSize);
            param.Add("@gridWidth", flt.gridWidth);
            return param;
        }
        private DynamicParameters GetParameters(DynamicParameters param = null, object objParams = null,
            ParametersHandler parametersHandler = null)
        {
            param ??= new DynamicParameters();
            if (objParams != null)
                param.AddDynamicParams(objParams);
            param = parametersHandler != null ? parametersHandler(param) : param;

            var lsParamName = param.ParameterNames.Select(s => s).ToList();
            if (lsParamName.FirstOrDefault(s => s.ToLower() == "UserId".ToLower()) == null)
                param.Add("UserId", CommonInfo.UserId);
            if (CommonInfo.IsAcceptLanguage && lsParamName.FirstOrDefault(s => s.ToLower() == "AcceptLanguage".ToLower()) == null)
                param.Add("AcceptLanguage", CommonInfo.AcceptLanguage);
            return param;
        }
    }
}